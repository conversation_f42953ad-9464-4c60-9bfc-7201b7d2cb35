{"name": "lapp-dashboard", "productName": "JOZI1 Lapp Dashboard", "version": "1.0.0", "description": "Electron Forge with shadcn-ui (Vite + Typescript)", "main": "dist/main/electron-main.js", "private": true, "scripts": {"start": "electron-forge start", "start:dev": "npm run backend:prepare && concurrently -k -s first -n APP,BACKEND \"npm:dev:app\" \"npm:dev:backend\"", "dev": "npm run start:dev", "dev:app": "electron-forge start", "dev:backend": "npm --prefix backend run dev", "backend:prepare": "npm --prefix backend i && npm --prefix backend run prisma:generate", "postinstall": "electron-forge import || echo skip", "dev:frontend": "vite", "package": "electron-forge package", "make": "electron-forge make", "publish": "electron-forge publish", "build:portable": "electron-builder --win portable --publish never", "build:portable-new": "electron-builder --config electron-builder.js", "build:portable-complete": "npm run build:forge && electron-builder --config electron-builder.js", "build:simple-portable": "npx electron-packager . LappDashboard --platform=win32 --arch=x64 --out=portable-build --overwrite", "create:portable": "npm run make && npx electron-builder --config=electron-builder-simple.js", "build:quick-portable": "electron-builder --win portable --config.directories.output=portable-dist --config.win.target=portable --config.portable.artifactName=LappDashboard-portable.exe", "test:watch": "vitest watch", "test:unit": "vitest", "test:e2e": "playwright test", "test:all": "vitest run && playwright test", "test:ai": "vitest run src/modules/ai/__tests__/ai-comprehensive-test-suite.test.ts", "test:ai:comprehensive": "tsx src/modules/ai/__tests__/run-comprehensive-tests.ts", "test:ai:performance": "vitest run src/modules/ai/__tests__/performance/", "test:ai:load": "vitest run src/modules/ai/__tests__/load/", "test:ai:accuracy": "vitest run src/modules/ai/__tests__/accuracy/", "test:ai:integration": "vitest run src/modules/ai/__tests__/integration/", "test:ai:coverage": "vitest run src/modules/ai/__tests__/coverage/", "test:ai:deployment": "vitest run src/modules/ai/deployment/AIModuleDeploymentTests.test.ts", "deploy:ai": "tsx src/modules/ai/deployment/deploy-ai-module.ts", "validate:ai:deployment": "tsx scripts/validate-ai-deployment.ts", "validate:ai:deployment:verbose": "tsx scripts/validate-ai-deployment.ts --verbose", "validate:ai:deployment:json": "tsx scripts/validate-ai-deployment.ts --json", "health:ai": "tsx -e \"import { aiModuleHealthCheck } from './src/modules/ai/monitoring/AIModuleHealthCheck'; aiModuleHealthCheck.performHealthCheck().then(r => console.log(JSON.stringify(r, null, 2)))\"", "generate:routes": "tsx node_modules/@tanstack/router-cli/cli.ts generate", "validate:portable": "tsx scripts/build-system/portable-checks.ts"}, "author": "<PERSON>", "license": "MIT", "devDependencies": {"@electron-forge/cli": "^7.8.3", "@electron-forge/maker-deb": "^7.8.3", "@electron-forge/maker-rpm": "^7.8.3", "@electron-forge/maker-squirrel": "^7.8.3", "@electron-forge/maker-zip": "^7.8.3", "@electron-forge/plugin-auto-unpack-natives": "^7.8.3", "@electron-forge/plugin-fuses": "^7.8.3", "@electron-forge/plugin-vite": "^7.8.0", "@electron-forge/shared-types": "^7.8.0", "@electron/fuses": "^1.8.0", "@eslint/compat": "^1.2.8", "@eslint/js": "^9.25.1", "@playwright/test": "^1.52.0", "@stagewise/toolbar": "^0.4.8", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.7", "@tailwindcss/typography": "^0.5.16", "@tanstack/router-cli": "^1.121.2", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/better-sqlite3": "^7.6.13", "@types/electron-squirrel-startup": "^1.0.2", "@types/eslint-config-prettier": "^6.11.3", "@types/node": "^22.15.19", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "@types/react-syntax-highlighter": "^15.5.13", "@types/tailwindcss": "^3.0.11", "@vitejs/plugin-react": "^4.5.1", "autoprefixer": "^10.4.21", "clsx": "^2.1.1", "concurrently": "^9.2.0", "electron": "^35.7.2", "electron-devtools-installer": "^4.0.0", "electron-playwright-helpers": "^1.7.1", "electron-rebuild": "^3.2.9", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-compiler": "^19.1.0-rc.2", "framer-motion": "^12.23.12", "globals": "^16.0.0", "jsdom": "^26.0.0", "path-browserify": "^1.0.1", "postcss": "^8.5.3", "postcss-import": "^16.1.0", "postcss-nesting": "^13.0.1", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "tailwindcss-animate": "^1.0.7", "ts-node": "^10.9.2", "ts-prune": "^0.10.3", "typescript": "^5.8.3", "typescript-eslint": "^8.31.0", "vite": "^6.3.5", "vitest": "^3.1.2"}, "dependencies": {"@ai-sdk/react": "^2.0.9", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/babel-plugin": "^11.13.5", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@icons-pack/react-simple-icons": "^12.9.0", "@prisma/client": "^6.11.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@radix-ui/react-use-controllable-state": "^1.2.2", "@shikijs/transformers": "^3.9.1", "@tabler/icons-react": "^3.34.0", "@tailwindcss/vite": "^4.1.4", "@tanstack/react-query": "^5.74.7", "@tanstack/react-router": "^1.121.2", "@tanstack/react-table": "^8.21.3", "@tanstack/router-devtools": "^1.117.1", "@types/react-router-dom": "^5.3.3", "@types/sqlite3": "^3.1.11", "ai": "^5.0.9", "animejs": "^4.0.2", "axios": "^1.11.0", "bcryptjs": "^3.0.2", "better-sqlite3": "^12.2.0", "class-variance-authority": "^0.7.1", "cmdk": "^1.1.1", "cors": "^2.8.5", "csv-parse": "^5.6.0", "date-fns": "^3.6.0", "dotenv": "^17.0.1", "dotted-map": "^2.2.3", "electron-squirrel-startup": "^1.0.1", "embla-carousel-react": "^8.6.0", "express": "^5.1.0", "harden-react-markdown": "^1.0.4", "i18next": "^25.0.1", "jsonwebtoken": "^9.0.2", "katex": "^0.16.22", "lottie-react": "^2.4.1", "lucide-react": "^0.294.0", "motion": "^12.23.12", "node-fetch": "^2.7.0", "openai": "^5.8.2", "prisma": "^6.11.1", "radix-ui": "^1.4.3", "react": "^18.2.0", "react-day-picker": "^9.7.0", "react-dom": "18.2.0", "react-i18next": "^15.5.1", "react-markdown": "^10.1.0", "react-router-dom": "^6.30.1", "react-syntax-highlighter": "^15.6.1", "recharts": "^2.15.3", "rehype-katex": "^7.0.1", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "shiki": "^3.9.1", "sonner": "^2.0.5", "sqlite": "^5.1.1", "sqlite-vss": "^0.1.2", "sqlite3": "^5.1.7", "styled-components": "^6.1.19", "swiper": "^11.2.10", "tw-animate-css": "^1.2.9", "use-stick-to-bottom": "^1.1.1", "vaul": "^1.1.2", "zod": "^3.25.76"}, "build": {"appId": "com.jozilapp.dashboard", "productName": "JOZI1 Lapp Dashboard", "win": {"target": "portable", "icon": "assets/lapp.ico"}, "portable": {"artifactName": "LappDashboard-portable-${version}.exe"}, "directories": {"output": "dist"}, "files": ["main.js", "src/**/*", "package.json", "!src/**/*.ts", "!database/sfm_dashboard.db", "!**/*.db", "!**/*.sqlite*"], "extraResources": ["database/**/*", "!database/**/*.db"]}, "engines": {"node": ">=20.12 <21"}, "config": {"forge": "forge.config.ts"}}